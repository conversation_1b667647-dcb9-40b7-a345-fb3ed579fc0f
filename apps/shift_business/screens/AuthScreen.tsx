import { View, Text, TextInput, Button, StyleSheet } from 'react-native';
import { useState } from 'react';
import { supabase } from '../services/auth';
import { getCurrentUser } from '../services/auth';

export default function AuthScreen({ route, navigation }: any) {
  
    const { setUser } = route.params;
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');

    const handleLogin = async () => {
        const { error } = await supabase.auth.signInWithPassword({ email, password });
        if (error) alert('Erreur : ' + error.message);
        else getCurrentUser().then((u) => {
            setUser(u);
        });
    };

    

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Connexion</Text>
            <TextInput placeholder="Email" style={styles.input} onChangeText={setEmail} value={email} />
            <TextInput placeholder="Mot de passe" secureTextEntry style={styles.input} onChangeText={setPassword} value={password} />
            <Button title="Se connecter" onPress={handleLogin} />
        </View>
    );
}

const styles = StyleSheet.create({
container: { flex: 1, justifyContent: 'center', padding: 20 },
title: { fontSize: 24, marginBottom: 20, textAlign: 'center' },
input: { borderWidth: 1, padding: 10, marginBottom: 15, borderRadius: 8 },
});
