import { View, Text, Button } from 'react-native';
import { supabase } from '../services/auth';

export default function HomeScreen() {
  const handleLogout = async () => {
    console.log("logout")
    await supabase.auth.signOut();
  };

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>Bienvenue !</Text>
      <Button title="Se déconnecter" onPress={handleLogout} />
    </View>
  );
}
